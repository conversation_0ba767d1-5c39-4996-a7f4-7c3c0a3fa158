import { invoke } from "@tauri-apps/api/core";

// 图书数据接口
interface Book {
  id: string;
  title: string;
  author: string;
  cover?: string;
  description?: string;
  status: 'reading' | 'finished' | 'wishlist';
  progress: number;
  addedDate: Date;
}

// 应用状态
class BookshelfApp {
  private books: Book[] = [];
  private currentView: string = 'all';
  private currentViewType: 'grid' | 'list' = 'grid';
  private searchQuery: string = '';

  constructor() {
    this.loadBooks();
    this.initializeEventListeners();
    this.renderBooks();
    this.updateStats();
  }

  // 初始化事件监听器
  private initializeEventListeners(): void {
    // 添加图书按钮
    const addBookBtn = document.getElementById('add-book-btn');
    addBookBtn?.addEventListener('click', () => this.showAddBookModal());

    // 搜索功能
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    searchInput?.addEventListener('input', (e) => {
      this.searchQuery = (e.target as HTMLInputElement).value;
      this.renderBooks();
    });

    // 导航菜单
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const view = (e.target as HTMLElement).dataset.view;
        if (view) {
          this.setActiveView(view);
        }
      });
    });

    // 视图切换
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const viewType = (e.target as HTMLElement).dataset.viewType as 'grid' | 'list';
        if (viewType) {
          this.setViewType(viewType);
        }
      });
    });

    // 排序
    const sortSelect = document.getElementById('sort-select') as HTMLSelectElement;
    sortSelect?.addEventListener('change', () => {
      this.renderBooks();
    });

    // 模态框
    this.initializeModal();
  }

  // 初始化模态框
  private initializeModal(): void {
    const modal = document.getElementById('add-book-modal');
    const closeBtn = document.getElementById('close-modal');
    const cancelBtn = document.getElementById('cancel-btn');
    const form = document.getElementById('add-book-form') as HTMLFormElement;

    closeBtn?.addEventListener('click', () => this.hideAddBookModal());
    cancelBtn?.addEventListener('click', () => this.hideAddBookModal());

    modal?.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.hideAddBookModal();
      }
    });

    form?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleAddBook();
    });
  }

  // 显示添加图书模态框
  private showAddBookModal(): void {
    const modal = document.getElementById('add-book-modal');
    modal?.classList.add('show');
  }

  // 隐藏添加图书模态框
  private hideAddBookModal(): void {
    const modal = document.getElementById('add-book-modal');
    modal?.classList.remove('show');
    this.resetForm();
  }

  // 重置表单
  private resetForm(): void {
    const form = document.getElementById('add-book-form') as HTMLFormElement;
    form?.reset();
  }

  // 处理添加图书
  private handleAddBook(): void {
    const titleInput = document.getElementById('book-title') as HTMLInputElement;
    const authorInput = document.getElementById('book-author') as HTMLInputElement;
    const coverInput = document.getElementById('book-cover') as HTMLInputElement;
    const descriptionInput = document.getElementById('book-description') as HTMLTextAreaElement;
    const statusInput = document.getElementById('book-status') as HTMLSelectElement;
    const progressInput = document.getElementById('book-progress') as HTMLInputElement;

    const book: Book = {
      id: Date.now().toString(),
      title: titleInput.value.trim(),
      author: authorInput.value.trim() || '未知作者',
      cover: coverInput.value.trim() || undefined,
      description: descriptionInput.value.trim() || undefined,
      status: statusInput.value as Book['status'],
      progress: parseInt(progressInput.value) || 0,
      addedDate: new Date()
    };

    if (!book.title) {
      alert('请输入书名');
      return;
    }

    this.books.push(book);
    this.saveBooks();
    this.renderBooks();
    this.updateStats();
    this.hideAddBookModal();
  }

  // 设置活动视图
  private setActiveView(view: string): void {
    this.currentView = view;

    // 更新导航状态
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`)?.classList.add('active');

    this.renderBooks();
  }

  // 设置视图类型
  private setViewType(viewType: 'grid' | 'list'): void {
    this.currentViewType = viewType;

    // 更新按钮状态
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-view-type="${viewType}"]`)?.classList.add('active');

    // 更新网格类名
    const bookGrid = document.getElementById('book-grid');
    if (bookGrid) {
      bookGrid.className = viewType === 'grid' ? 'book-grid' : 'book-list';
    }

    this.renderBooks();
  }

  // 过滤图书
  private getFilteredBooks(): Book[] {
    let filtered = [...this.books];

    // 按视图过滤
    if (this.currentView !== 'all') {
      filtered = filtered.filter(book => book.status === this.currentView);
    }

    // 按搜索查询过滤
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(book =>
        book.title.toLowerCase().includes(query) ||
        book.author.toLowerCase().includes(query)
      );
    }

    // 排序
    const sortSelect = document.getElementById('sort-select') as HTMLSelectElement;
    const sortBy = sortSelect?.value || 'title';

    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'author':
          return a.author.localeCompare(b.author);
        case 'date':
          return b.addedDate.getTime() - a.addedDate.getTime();
        case 'progress':
          return b.progress - a.progress;
        default:
          return a.title.localeCompare(b.title);
      }
    });

    return filtered;
  }

  // 渲染图书
  private renderBooks(): void {
    const bookGrid = document.getElementById('book-grid');
    const emptyState = document.getElementById('empty-state');

    if (!bookGrid || !emptyState) return;

    const filteredBooks = this.getFilteredBooks();

    if (filteredBooks.length === 0) {
      bookGrid.style.display = 'none';
      emptyState.style.display = 'block';
      return;
    }

    bookGrid.style.display = 'grid';
    emptyState.style.display = 'none';

    bookGrid.innerHTML = filteredBooks.map(book => this.createBookCard(book)).join('');
  }

  // 创建图书卡片
  private createBookCard(book: Book): string {
    const statusClass = `status-${book.status}`;
    const statusText = {
      reading: '正在阅读',
      finished: '已完成',
      wishlist: '想读'
    }[book.status];

    const coverStyle = book.cover
      ? `background-image: url('${book.cover}')`
      : '';

    return `
      <div class="book-card" data-book-id="${book.id}">
        <div class="book-cover ${book.cover ? '' : 'no-image'}" style="${coverStyle}">
          ${!book.cover ? '📖' : ''}
          ${book.progress > 0 ? `<div class="book-progress-overlay">${book.progress}% 完成</div>` : ''}
        </div>
        <div class="book-info">
          <h3 class="book-title">${book.title}</h3>
          <p class="book-author">${book.author}</p>
          <span class="book-status ${statusClass}">${statusText}</span>
          ${book.status === 'reading' && book.progress > 0 ? `
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${book.progress}%"></div>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }

  // 更新统计信息
  private updateStats(): void {
    const totalBooks = document.getElementById('total-books');
    const readingBooks = document.getElementById('reading-books');
    const finishedBooks = document.getElementById('finished-books');

    if (totalBooks) totalBooks.textContent = this.books.length.toString();
    if (readingBooks) readingBooks.textContent = this.books.filter(b => b.status === 'reading').length.toString();
    if (finishedBooks) finishedBooks.textContent = this.books.filter(b => b.status === 'finished').length.toString();
  }

  // 保存图书到本地存储
  private saveBooks(): void {
    localStorage.setItem('bookshelf-books', JSON.stringify(this.books));
  }

  // 从本地存储加载图书
  private loadBooks(): void {
    const saved = localStorage.getItem('bookshelf-books');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        this.books = parsed.map((book: any) => ({
          ...book,
          addedDate: new Date(book.addedDate)
        }));
      } catch (error) {
        console.error('Failed to load books:', error);
        this.books = [];
      }
    } else {
      // 添加一些示例数据
      this.books = this.createSampleBooks();
      this.saveBooks();
    }
  }

  // 创建示例图书数据
  private createSampleBooks(): Book[] {
    return [
      {
        id: '1',
        title: '三体',
        author: '刘慈欣',
        cover: 'https://img1.doubanio.com/view/subject/s/public/s2768378.jpg',
        description: '文化大革命如火如荼进行的同时，军方探寻外星文明的绝秘计划"红岸工程"取得了突破性进展。',
        status: 'reading',
        progress: 65,
        addedDate: new Date('2024-01-15')
      },
      {
        id: '2',
        title: '百年孤独',
        author: '加西亚·马尔克斯',
        cover: 'https://img1.doubanio.com/view/subject/s/public/s6384944.jpg',
        description: '魔幻现实主义文学的代表作，讲述了布恩迪亚家族七代人的传奇故事。',
        status: 'finished',
        progress: 100,
        addedDate: new Date('2024-01-10')
      },
      {
        id: '3',
        title: '1984',
        author: '乔治·奥威尔',
        description: '反乌托邦小说的经典之作，描绘了一个极权主义社会的恐怖景象。',
        status: 'wishlist',
        progress: 0,
        addedDate: new Date('2024-01-20')
      }
    ];
  }
}

// 初始化应用
window.addEventListener('DOMContentLoaded', () => {
  new BookshelfApp();
});
