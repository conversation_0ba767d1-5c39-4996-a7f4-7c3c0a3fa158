<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="/src/styles.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reading App - 我的书架</title>
    <script type="module" src="/src/main.ts" defer></script>
  </head>

  <body>
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <h1 class="app-title">📚 我的书架</h1>
        <div class="header-actions">
          <button class="btn btn-primary" id="add-book-btn">
            <span class="icon">+</span>
            添加图书
          </button>
          <div class="search-box">
            <input type="text" id="search-input" placeholder="搜索图书..." />
            <span class="search-icon">🔍</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 侧边栏 -->
      <aside class="sidebar">
        <nav class="nav-menu">
          <ul>
            <li><a href="#" class="nav-item active" data-view="all">📖 全部图书</a></li>
            <li><a href="#" class="nav-item" data-view="reading">📚 正在阅读</a></li>
            <li><a href="#" class="nav-item" data-view="finished">✅ 已完成</a></li>
            <li><a href="#" class="nav-item" data-view="wishlist">⭐ 想读</a></li>
          </ul>
        </nav>

        <div class="reading-stats">
          <h3>阅读统计</h3>
          <div class="stat-item">
            <span class="stat-label">总计图书</span>
            <span class="stat-value" id="total-books">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">正在阅读</span>
            <span class="stat-value" id="reading-books">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">已完成</span>
            <span class="stat-value" id="finished-books">0</span>
          </div>
        </div>
      </aside>

      <!-- 图书网格 -->
      <section class="book-grid-container">
        <div class="view-controls">
          <div class="view-options">
            <button class="view-btn active" data-view-type="grid">
              <span class="icon">⊞</span>
              网格视图
            </button>
            <button class="view-btn" data-view-type="list">
              <span class="icon">☰</span>
              列表视图
            </button>
          </div>
          <div class="sort-options">
            <select id="sort-select">
              <option value="title">按标题排序</option>
              <option value="author">按作者排序</option>
              <option value="date">按添加时间排序</option>
              <option value="progress">按阅读进度排序</option>
            </select>
          </div>
        </div>

        <div class="book-grid" id="book-grid">
          <!-- 图书卡片将在这里动态生成 -->
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="empty-state">
          <div class="empty-icon">📚</div>
          <h3>还没有添加任何图书</h3>
          <p>点击"添加图书"按钮开始构建您的个人图书馆</p>
          <button class="btn btn-primary" onclick="document.getElementById('add-book-btn').click()">
            添加第一本书
          </button>
        </div>
      </section>
    </main>

    <!-- 添加图书模态框 -->
    <div class="modal" id="add-book-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>添加新图书</h2>
          <button class="close-btn" id="close-modal">&times;</button>
        </div>
        <form class="modal-body" id="add-book-form">
          <div class="form-group">
            <label for="book-title">书名 *</label>
            <input type="text" id="book-title" required />
          </div>
          <div class="form-group">
            <label for="book-author">作者</label>
            <input type="text" id="book-author" />
          </div>
          <div class="form-group">
            <label for="book-cover">封面图片URL</label>
            <input type="url" id="book-cover" placeholder="https://..." />
          </div>
          <div class="form-group">
            <label for="book-description">简介</label>
            <textarea id="book-description" rows="3"></textarea>
          </div>
          <div class="form-group">
            <label for="book-status">阅读状态</label>
            <select id="book-status">
              <option value="wishlist">想读</option>
              <option value="reading">正在阅读</option>
              <option value="finished">已完成</option>
            </select>
          </div>
          <div class="form-group">
            <label for="book-progress">阅读进度 (%)</label>
            <input type="number" id="book-progress" min="0" max="100" value="0" />
          </div>
        </form>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" id="cancel-btn">取消</button>
          <button type="submit" form="add-book-form" class="btn btn-primary">添加图书</button>
        </div>
      </div>
    </div>
  </body>
</html>
