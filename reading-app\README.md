# 📚 Reading App - 桌面读书应用

一个使用 Tauri + TypeScript 构建的现代化桌面读书应用，提供优雅的书架管理和阅读进度跟踪功能。

## ✨ 功能特性

### 📖 书架管理
- **图书添加**: 支持添加书名、作者、封面图片、简介等信息
- **多种视图**: 网格视图和列表视图切换
- **智能分类**: 按阅读状态分类（全部图书、正在阅读、已完成、想读）
- **搜索功能**: 快速搜索图书标题和作者
- **排序选项**: 支持按标题、作者、添加时间、阅读进度排序

### 📊 阅读统计
- **实时统计**: 显示总图书数、正在阅读数、已完成数
- **进度跟踪**: 可视化阅读进度条
- **状态管理**: 灵活的阅读状态切换

### 🎨 用户界面
- **现代设计**: 简洁优雅的界面设计
- **响应式布局**: 适配不同屏幕尺寸
- **中文优化**: 针对中文阅读习惯优化
- **深色模式**: 支持系统主题跟随

## 🚀 技术栈

- **前端框架**: Vanilla TypeScript + Vite
- **桌面框架**: Tauri 2.0
- **样式**: 现代 CSS + CSS Variables
- **数据存储**: LocalStorage（本地存储）
- **构建工具**: Vite + TypeScript

## 📦 安装和运行

### 环境要求
- Node.js 16+
- Rust 1.70+
- 系统要求：Windows 10+, macOS 10.15+, 或 Linux

### 开发环境设置

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd reading-app
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **运行开发服务器**
   ```bash
   npm run dev
   ```
   这将启动 Vite 开发服务器，可以在浏览器中预览界面

4. **运行 Tauri 开发模式**
   ```bash
   npm run tauri dev
   ```
   这将启动完整的桌面应用

### 构建生产版本

```bash
npm run tauri build
```

## 🎯 使用指南

### 添加图书
1. 点击右上角的"添加图书"按钮
2. 填写图书信息（书名为必填项）
3. 选择阅读状态和进度
4. 点击"添加图书"完成

### 管理图书
- **搜索**: 在搜索框中输入关键词快速查找
- **筛选**: 点击左侧导航切换不同的图书分类
- **排序**: 使用右上角的排序选项重新排列图书
- **视图**: 在网格视图和列表视图之间切换

### 阅读进度
- 在添加或编辑图书时设置阅读进度（0-100%）
- 进度会在图书卡片上以进度条形式显示
- 左侧统计面板实时更新阅读数据

## 🛠️ 开发说明

### 项目结构
```
reading-app/
├── src/                    # 前端源码
│   ├── main.ts            # 主要应用逻辑
│   ├── styles.css         # 样式文件
│   └── assets/            # 静态资源
├── src-tauri/             # Tauri 后端
│   ├── src/               # Rust 源码
│   ├── Cargo.toml         # Rust 依赖配置
│   └── tauri.conf.json    # Tauri 配置
├── index.html             # 主页面
├── package.json           # Node.js 依赖
└── vite.config.ts         # Vite 配置
```

### 核心功能实现
- **BookshelfApp 类**: 主要的应用状态管理
- **本地存储**: 使用 localStorage 持久化图书数据
- **响应式设计**: CSS Grid + Flexbox 布局
- **模态框**: 原生 JavaScript 实现的添加图书界面

## 🔧 自定义配置

### 修改应用信息
编辑 `src-tauri/tauri.conf.json` 文件：
```json
{
  "productName": "你的应用名称",
  "identifier": "com.yourcompany.yourapp"
}
```

### 添加新功能
1. 在 `src/main.ts` 中扩展 BookshelfApp 类
2. 在 `src/styles.css` 中添加相应样式
3. 在 `index.html` 中添加新的 UI 元素

## 📝 待实现功能

- [ ] 图书详情页面
- [ ] 阅读笔记功能
- [ ] 数据导入/导出
- [ ] 云同步支持
- [ ] 图书推荐系统
- [ ] 阅读时间统计
- [ ] 标签和分类管理

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

- [Tauri](https://tauri.app/) - 优秀的桌面应用框架
- [Vite](https://vitejs.dev/) - 快速的构建工具
- [TypeScript](https://www.typescriptlang.org/) - 类型安全的 JavaScript
