{"$schema": "https://schema.tauri.app/config/2", "productName": "reading-app", "version": "0.1.0", "identifier": "com.readingapp.desktop", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "Reading App - 我的书架", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}